#include "pch.h"
#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <iostream>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();

	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);


		//if (!player.enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
		//	continue;

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;


		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		auto closest_player = findClosest(playerPositions);
		if (!closest_player.IsZero())
		{
			MoveMouseToPlayer(closest_player);
		}
	}
}

Vector Aimbot::findClosest(const std::vector<Vector> playerPositions)
{
	if (playerPositions.empty()) return Vector{0,0,0};

	Vector center_of_screen{
		static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2,
		static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2,
		0.0f
	};
	

	float max_distance_sq = globals::Legitbot::radius * globals::Legitbot::radius;
	float closest_distance_sq = FLT_MAX;
	Vector closest = Vector{0,0,0};

	for (const auto& pos : playerPositions) {
		float dx = pos.x - center_of_screen.x;
		float dy = pos.y - center_of_screen.y;
		float distance_sq = dx*dx + dy*dy;

		if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
			closest_distance_sq = distance_sq;
			closest = pos;
		}
	}
	return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position)
{
	if (position.IsZero())
		return;

	POINT currentMousePos;
	GetCursorPos(&currentMousePos);
	Vector currentPos{
		static_cast<float>(currentMousePos.x),
		static_cast<float>(currentMousePos.y),
		0.0f
	};

	float deltaX = position.x - currentPos.x;
	float deltaY = position.y - currentPos.y;

	// Bei Smoothness 1 sofort auf den Kopf (keine Glättung)
	if (globals::Legitbot::smoothness <= 1.0f) {
		mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
			static_cast<LONG>(std::round(deltaY)), 0, 0);
	}

	const float base_smoothness = max(globals::Legitbot::smoothness, 1.0f);
	const float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);
	const float adaptive_smoothness = base_smoothness * (1.0f + distance / 1000.0f);

	float stepX = deltaX / adaptive_smoothness;
	float stepY = deltaY / adaptive_smoothness;

	accumulatedX += stepX;
	accumulatedY += stepY;

	LONG moveX = static_cast<LONG>(std::round(accumulatedX));
	LONG moveY = static_cast<LONG>(std::round(accumulatedY));

	accumulatedX -= moveX;
	accumulatedY -= moveY;

	const float deadzone = (0.2f * adaptive_smoothness < 2.0f) ? 0.2f * adaptive_smoothness : 2.0f;
	if (std::abs(deltaX) < deadzone && std::abs(deltaY) < deadzone) {
		accumulatedX += deltaX;
		accumulatedY += deltaY;
		return;
	}

	mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
}